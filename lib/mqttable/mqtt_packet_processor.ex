defmodule Mqttable.MqttPacketProcessor do
  @moduledoc """
  Module for processing MQTT packets from telemetry handlers.
  This module contains the logic for parsing and processing MQTT packets from telemetry events.
  """

  require Logger
  alias <PERSON>.PubSub
  alias Mqttable.MqttClient.Manager
  alias Mqttable.TraceManager

  @pubsub_topic "mqtt_trace"

  @doc """
  Process emqtt report data from telemetry sources.
  """
  def process_emqtt_report(%{msg: ~c"send_data"} = report) do
    packet = Map.get(report, :packet)
    client_id = Map.get(report, :clientid, "unknown")
    data_size = Map.get(report, :data_size, 0)
    name = Map.get(report, :broker_name)
    IO.inspect(report)

    # Process the captured data
    process_send_data(name, packet, client_id, data_size)
  end

  def process_emqtt_report(%{msg: ~c"send_data_failed"} = report) do
    reason = Map.get(report, :reason)
    packet = Map.get(report, :packet)
    client_id = Map.get(report, :clientid, "unknown")
    data_size = Map.get(report, :data_size, 0)
    name = Map.get(report, :broker_name)

    # Process the failed data
    process_send_data_failed(name, reason, packet, client_id, data_size)
  end

  def process_emqtt_report(%{msg: ~c"recv_data"} = report) do
    data = Map.get(report, :data)
    client_id = Map.get(report, :clientid, "unknown")
    data_size = Map.get(report, :data_size, 0)
    name = Map.get(report, :broker_name)

    # Process the received data
    parse_state = Manager.lookup_client_parse_state(client_id)
    process_recv_data(name, data, parse_state, client_id, data_size)
  end

  def process_emqtt_report(%{msg: ~c"websocket_recv_data"} = report) do
    data = Map.get(report, :data)
    client_id = Map.get(report, :clientid, "unknown")
    data_size = Map.get(report, :data_size, 0)
    name = Map.get(report, :broker_name)

    # Process the received data
    parse_state = Manager.lookup_client_parse_state(client_id)
    process_recv_data(name, data, parse_state, client_id, data_size)
  end

  def process_emqtt_report(_other_report) do
    # Ignore other emqtt reports
    :ok
  end

  @doc """
  Convert technical error payloads to user-friendly messages for display.
  This function takes raw error payloads and converts them to messages users can understand.
  """
  def format_connection_error_payload(payload) when is_binary(payload) do
    # Check if the payload contains technical Erlang error terms
    cond do
      String.contains?(payload, "tcp_closed") ->
        "Connection was closed by the broker before completing the handshake"

      String.contains?(payload, "econnrefused") ->
        "Connection refused - The broker is not reachable or not running"

      String.contains?(payload, "timeout") ->
        "Connection timeout - The broker did not respond in time"

      String.contains?(payload, "nxdomain") ->
        "Host not found - The broker hostname could not be resolved"

      String.contains?(payload, "econnreset") ->
        "Connection reset - The connection was forcibly closed by the broker"

      String.contains?(payload, "ehostunreach") ->
        "Host unreachable - The broker host is unreachable"

      String.contains?(payload, "enetunreach") ->
        "Network unreachable - The network is unreachable"

      String.contains?(payload, "etimedout") ->
        "Connection timed out - The operation timed out"

      String.contains?(payload, "closed") ->
        "Connection closed - The connection was closed unexpectedly"

      # If it's already a user-friendly message, return as-is
      true ->
        payload
    end
  end

  def format_connection_error_payload(payload), do: payload || "Unknown connection error"

  @doc """
  Create and broadcast a connection error trace message.
  This function creates a user-friendly trace message for connection failures.
  """
  def create_connection_error_trace(broker_name, client_id, error_reason, error_message) do
    # Create a trace message for the connection error
    trace_message = %{
      id: System.unique_integer([:positive, :monotonic]),
      timestamp: DateTime.to_unix(DateTime.utc_now(), :microsecond),
      broker_name: broker_name,
      client_id: client_id,
      type: "CONNECTION_ERROR",
      direction: "SYSTEM",
      topic: "",
      payload: error_message,
      data_size: 0,
      payload_size: 0,
      qos: "",
      retain: false,
      packet_id: nil,
      properties: %{},
      reason_code: nil,
      extra_info: %{
        error_reason: inspect(error_reason),
        error_type: "connection_failure"
      }
    }

    # Store the message in the appropriate broker's ETS table
    TraceManager.store_trace_message(client_id, trace_message)

    # Also broadcast the trace message for real-time updates
    PubSub.broadcast(Mqttable.PubSub, @pubsub_topic, {:mqtt_trace_message, trace_message})
  end

  # Process send data
  defp process_send_data(name, packet, client_id, data_size) when is_binary(client_id) do
    case packet do
      nil ->
        Logger.warning("Received send_data event with nil packet for client #{client_id}")
        :ok

      packet ->
        process_mqtt_packet(name, packet, client_id, "OUT", data_size)
    end
  end

  # Handle invalid client_id
  defp process_send_data(name, _packet, client_id, _data_size) do
    Logger.warning(
      "Invalid client_id for send_data processing: #{inspect(client_id)} #{inspect(name)}"
    )

    :ok
  end

  # Process failed send data
  defp process_send_data_failed(name, reason, _packet, client_id, _data_size) do
    Logger.error("#{inspect(name)} Send Data Failed from client #{client_id}: #{inspect(reason)}")
  end

  # Process received data
  defp process_recv_data(name, data, state, client_id, data_size)
       when is_binary(data) and is_binary(client_id) and not is_nil(state) do
    try do
      case :emqtt_frame.parse(data, state) do
        {:ok, packet, "", new_parse_state} ->
          process_mqtt_packet(name, packet, client_id, "IN", data_size)
          Manager.store_client_parse_state(client_id, new_parse_state)

        {:ok, packet, rest, new_parse_state} ->
          process_mqtt_packet(name, packet, client_id, "IN", data_size)
          process_recv_data(name, rest, new_parse_state, client_id, data_size)

        {:more, new_parse_state} ->
          Manager.store_client_parse_state(client_id, new_parse_state)
      end
    catch
      :exit, reason ->
        Logger.error("EMQTT Receive Data Failed from client #{client_id}: #{inspect(reason)}")
        false
    end
  end

  defp process_recv_data(_name, data, nil, client_id, _data_size) do
    Logger.error("Parse state is nil for client #{client_id} #{inspect(data)}")
    false
  end

  # Handle invalid data or client_id
  defp process_recv_data(_name, data, _parse_state, client_id, _data_size) do
    Logger.warning(
      "Invalid data or client_id for recv_data processing: data=#{inspect(data)}, client_id=#{inspect(client_id)}"
    )

    false
  end

  # Process MQTT packet and forward to trace component for all packet types
  defp process_mqtt_packet(name, packet, client_id, direction, data_size) do
    case packet do
      # Match CONNECT packet
      {:mqtt_packet, {:mqtt_packet_header, 1, _dup, qos, retain}, variable, payload} ->
        format_and_broadcast_trace_message(
          name,
          client_id,
          "CONNECT",
          qos,
          retain,
          variable,
          payload,
          direction,
          data_size
        )

      # Match CONNACK packet
      {:mqtt_packet, {:mqtt_packet_header, 2, _dup, qos, retain}, variable, payload} ->
        format_and_broadcast_trace_message(
          name,
          client_id,
          "CONNACK",
          qos,
          retain,
          variable,
          payload,
          direction,
          data_size
        )

      # Match PUBLISH packet
      {:mqtt_packet, {:mqtt_packet_header, 3, _dup, qos, retain}, variable, payload} ->
        format_and_broadcast_trace_message(
          name,
          client_id,
          "PUBLISH",
          qos,
          retain,
          variable,
          payload,
          direction,
          data_size
        )

      # Match PUBACK packet
      {:mqtt_packet, {:mqtt_packet_header, 4, _dup, qos, retain}, variable, payload} ->
        format_and_broadcast_trace_message(
          name,
          client_id,
          "PUBACK",
          qos,
          retain,
          variable,
          payload,
          direction,
          data_size
        )

      # Match PUBREC packet
      {:mqtt_packet, {:mqtt_packet_header, 5, _dup, qos, retain}, variable, payload} ->
        format_and_broadcast_trace_message(
          name,
          client_id,
          "PUBREC",
          qos,
          retain,
          variable,
          payload,
          direction,
          data_size
        )

      # Match PUBREL packet
      {:mqtt_packet, {:mqtt_packet_header, 6, _dup, qos, retain}, variable, payload} ->
        format_and_broadcast_trace_message(
          name,
          client_id,
          "PUBREL",
          qos,
          retain,
          variable,
          payload,
          direction,
          data_size
        )

      # Match PUBCOMP packet
      {:mqtt_packet, {:mqtt_packet_header, 7, _dup, qos, retain}, variable, payload} ->
        format_and_broadcast_trace_message(
          name,
          client_id,
          "PUBCOMP",
          qos,
          retain,
          variable,
          payload,
          direction,
          data_size
        )

      # Match SUBSCRIBE packet
      {:mqtt_packet, {:mqtt_packet_header, 8, _dup, qos, retain}, variable, payload} ->
        format_and_broadcast_trace_message(
          name,
          client_id,
          "SUBSCRIBE",
          qos,
          retain,
          variable,
          payload,
          direction,
          data_size
        )

      # Match SUBACK packet
      {:mqtt_packet, {:mqtt_packet_header, 9, _dup, qos, retain}, variable, payload} ->
        format_and_broadcast_trace_message(
          name,
          client_id,
          "SUBACK",
          qos,
          retain,
          variable,
          payload,
          direction,
          data_size
        )

      # Match UNSUBSCRIBE packet
      {:mqtt_packet, {:mqtt_packet_header, 10, _dup, qos, retain}, variable, payload} ->
        format_and_broadcast_trace_message(
          name,
          client_id,
          "UNSUBSCRIBE",
          qos,
          retain,
          variable,
          payload,
          direction,
          data_size
        )

      # Match UNSUBACK packet
      {:mqtt_packet, {:mqtt_packet_header, 11, _dup, qos, retain}, variable, payload} ->
        format_and_broadcast_trace_message(
          name,
          client_id,
          "UNSUBACK",
          qos,
          retain,
          variable,
          payload,
          direction,
          data_size
        )

      # Match PINGREQ packet
      {:mqtt_packet, {:mqtt_packet_header, 12, _dup, qos, retain}, variable, payload} ->
        format_and_broadcast_trace_message(
          name,
          client_id,
          "PINGREQ",
          qos,
          retain,
          variable,
          payload,
          direction,
          data_size
        )

      # Match PINGRESP packet
      {:mqtt_packet, {:mqtt_packet_header, 13, _dup, qos, retain}, variable, payload} ->
        format_and_broadcast_trace_message(
          name,
          client_id,
          "PINGRESP",
          qos,
          retain,
          variable,
          payload,
          direction,
          data_size
        )

      # Match DISCONNECT packet
      {:mqtt_packet, {:mqtt_packet_header, 14, _dup, qos, retain}, variable, payload} ->
        format_and_broadcast_trace_message(
          name,
          client_id,
          "DISCONNECT",
          qos,
          retain,
          variable,
          payload,
          direction,
          data_size
        )

      # Match AUTH packet
      {:mqtt_packet, {:mqtt_packet_header, 15, _dup, qos, retain}, variable, payload} ->
        format_and_broadcast_trace_message(
          name,
          client_id,
          "AUTH",
          qos,
          retain,
          variable,
          payload,
          direction,
          data_size
        )

      # For unknown packet types
      _ ->
        :ok
    end
  end

  # Format and broadcast trace message for all packet types
  defp format_and_broadcast_trace_message(
         name,
         client_id,
         packet_type,
         qos,
         retain,
         variable,
         payload,
         direction,
         data_size
       ) do
    # Extract packet-specific information
    {topic, packet_id, properties, reason_code, extra_info} =
      extract_packet_info(packet_type, variable)

    # Format payload - for ACK messages, use reason code if payload is empty
    formatted_payload = format_payload_with_reason_code(payload, packet_type, reason_code)

    # Calculate actual payload size (not the formatted display text size)
    actual_payload_size = calculate_actual_payload_size(payload, packet_type)

    # Get current timestamp as integer (microseconds since Unix epoch)
    now = DateTime.utc_now()
    timestamp_int = DateTime.to_unix(now, :microsecond)

    # Format the message for the trace component
    trace_message = %{
      id: System.unique_integer([:positive, :monotonic]),
      broker_name: name,
      timestamp: timestamp_int,
      client_id: client_id,
      type: packet_type,
      direction: direction,
      topic: topic,
      payload: formatted_payload,
      data_size: data_size,
      payload_size: actual_payload_size,
      qos: format_qos(qos, packet_type),
      retain: retain,
      packet_id: packet_id,
      properties: properties,
      reason_code: reason_code,
      extra_info: extra_info
    }

    # Store the message in the appropriate broker's ETS table
    TraceManager.store_trace_message(client_id, trace_message)

    # Also broadcast the trace message for real-time updates
    PubSub.broadcast(Mqttable.PubSub, @pubsub_topic, {:mqtt_trace_message, trace_message})
  end

  # Extract packet-specific information based on packet type and variable part
  defp extract_packet_info(packet_type, variable) do
    case {packet_type, variable} do
      # PUBLISH packet
      {"PUBLISH", {:mqtt_packet_publish, topic, packet_id, properties}} ->
        {topic, packet_id, properties, nil, nil}

      # SUBSCRIBE packet
      {"SUBSCRIBE", {:mqtt_packet_subscribe, packet_id, properties, topic_filters}} ->
        topic = format_topic_filters(topic_filters)
        {topic, packet_id, properties, nil, topic_filters}

      # UNSUBSCRIBE packet
      {"UNSUBSCRIBE", {:mqtt_packet_unsubscribe, packet_id, properties, topic_filters}} ->
        topic = format_topic_filters(topic_filters)
        {topic, packet_id, properties, nil, topic_filters}

      # SUBACK packet
      {"SUBACK", {:mqtt_packet_suback, packet_id, properties, reason_codes}} ->
        {"", packet_id, properties, reason_codes, nil}

      # UNSUBACK packet
      {"UNSUBACK", {:mqtt_packet_unsuback, packet_id, properties, reason_codes}} ->
        {"", packet_id, properties, reason_codes, nil}

      # PUBACK, PUBREC, PUBREL, PUBCOMP packets
      {type, {:mqtt_packet_puback, packet_id, reason_code, properties}}
      when type in ["PUBACK", "PUBREC", "PUBREL", "PUBCOMP"] ->
        {"", packet_id, properties, reason_code, nil}

      # CONNACK packet
      {"CONNACK", {:mqtt_packet_connack, ack_flags, reason_code, properties}} ->
        {"", nil, properties, reason_code, ack_flags}

      # DISCONNECT packet
      {"DISCONNECT", {:mqtt_packet_disconnect, reason_code, properties}} ->
        {"", nil, properties, reason_code, nil}

      # AUTH packet
      {"AUTH", {:mqtt_packet_auth, reason_code, properties}} ->
        {"", nil, properties, reason_code, nil}

      # CONNECT packet
      {"CONNECT",
       {:mqtt_packet_connect, proto_name, proto_ver, is_bridge, clean_start, will_flag, will_qos,
        will_retain, keepalive, properties, clientid, will_props, will_topic, will_payload,
        username, password}} ->
        connect_info = %{
          proto_name: proto_name,
          proto_ver: proto_ver,
          is_bridge: is_bridge,
          clean_start: clean_start,
          will_flag: will_flag,
          will_qos: will_qos,
          will_retain: will_retain,
          keepalive: keepalive,
          clientid: clientid,
          will_props: will_props,
          will_topic: will_topic,
          will_payload: will_payload,
          username: username,
          password: password
        }

        {"", nil, properties, nil, connect_info}

      # For packets with simple variable part or unknown structure
      _ ->
        {"", nil, nil, nil, nil}
    end
  end

  # Format topic filters for SUBSCRIBE/UNSUBSCRIBE packets (for display in topic field)
  defp format_topic_filters(topic_filters) when is_list(topic_filters) do
    Enum.map_join(topic_filters, ", ", fn
      {topic, _opts} -> topic
      topic when is_binary(topic) -> topic
      _ -> "unknown"
    end)
  end

  defp format_topic_filters(_), do: ""

  # Format payload for display - enhanced version that handles ACK messages with reason codes
  defp format_payload_with_reason_code(payload, packet_type, reason_code) do
    # Check if this is an ACK message type
    ack_types = [
      "PUBACK",
      "PUBREC",
      "PUBREL",
      "PUBCOMP",
      "SUBACK",
      "UNSUBACK",
      "CONNACK",
      "DISCONNECT"
    ]

    if packet_type in ack_types do
      # For ACK messages, if payload is empty, use reason code information
      formatted_payload = format_payload(payload)

      if formatted_payload == "" do
        format_reason_code_as_payload(packet_type, reason_code)
      else
        formatted_payload
      end
    else
      # For non-ACK messages, use standard payload formatting
      format_payload(payload)
    end
  end

  # Format payload for display
  defp format_payload(payload) when is_binary(payload), do: payload
  defp format_payload(nil), do: ""
  defp format_payload(_), do: ""

  # Format reason code as payload content for ACK messages
  defp format_reason_code_as_payload(packet_type, reason_code)
       when packet_type in ["PUBACK", "PUBREC", "PUBREL", "PUBCOMP"] do
    case reason_code do
      0 -> "Success"
      16 -> "No matching subscribers"
      128 -> "Unspecified error"
      129 -> "Implementation specific error"
      135 -> "Not authorized"
      144 -> "Topic Name invalid"
      145 -> "Packet identifier in use"
      146 -> "Packet identifier not found"
      147 -> "Receive Maximum exceeded"
      148 -> "Topic Alias invalid"
      149 -> "Packet too large"
      150 -> "Message rate too high"
      151 -> "Quota exceeded"
      152 -> "Administrative action"
      153 -> "Payload format invalid"
      154 -> "Retain not supported"
      155 -> "QoS not supported"
      156 -> "Use another server"
      157 -> "Server moved"
      158 -> "Shared Subscriptions not supported"
      159 -> "Connection rate exceeded"
      160 -> "Maximum connect time"
      161 -> "Subscription Identifiers not supported"
      162 -> "Wildcard Subscriptions not supported"
      _ -> "Unknown (#{reason_code})"
    end
  end

  defp format_reason_code_as_payload("CONNACK", reason_code) do
    case reason_code do
      0 -> "Connection Accepted"
      128 -> "Unspecified error"
      129 -> "Malformed Packet"
      130 -> "Protocol Error"
      131 -> "Implementation specific error"
      132 -> "Unsupported Protocol Version"
      133 -> "Client Identifier not valid"
      134 -> "Bad User Name or Password"
      135 -> "Not authorized"
      136 -> "Server unavailable"
      137 -> "Server busy"
      138 -> "Banned"
      140 -> "Bad authentication method"
      _ -> "Connection Refused (#{reason_code})"
    end
  end

  defp format_reason_code_as_payload("DISCONNECT", reason_code) do
    case reason_code do
      0 -> "Normal disconnection"
      1 -> "Disconnect with Will Message"
      128 -> "Unspecified error"
      129 -> "Malformed Packet"
      130 -> "Protocol Error"
      131 -> "Implementation specific error"
      135 -> "Not authorized"
      137 -> "Server busy"
      138 -> "Banned"
      139 -> "Server shutting down"
      140 -> "Bad authentication method"
      141 -> "Keep Alive timeout"
      142 -> "Session taken over"
      143 -> "Topic Filter invalid"
      144 -> "Topic Name invalid"
      147 -> "Receive Maximum exceeded"
      148 -> "Topic Alias invalid"
      149 -> "Packet too large"
      150 -> "Message rate too high"
      151 -> "Quota exceeded"
      152 -> "Administrative action"
      153 -> "Payload format invalid"
      _ -> "Disconnect (#{reason_code})"
    end
  end

  defp format_reason_code_as_payload(packet_type, reason_codes)
       when packet_type in ["SUBACK", "UNSUBACK"] and is_list(reason_codes) do
    success_count = Enum.count(reason_codes, &(&1 == 0))
    total_count = length(reason_codes)

    cond do
      success_count == total_count ->
        # All successful
        if packet_type == "SUBACK" do
          "All Subscriptions Successful"
        else
          "All Unsubscriptions Successful"
        end

      success_count == 0 ->
        # All failed - show specific error message
        if packet_type == "SUBACK" do
          error_message = format_subscription_error(reason_codes)
          "Subscription Failed: #{error_message}"
        else
          error_message = format_unsubscription_error(reason_codes)
          "Unsubscription Failed: #{error_message}"
        end

      true ->
        # Partial success
        if packet_type == "SUBACK" do
          "#{success_count}/#{total_count} Subscriptions Successful"
        else
          "#{success_count}/#{total_count} Unsubscriptions Successful"
        end
    end
  end

  defp format_reason_code_as_payload(_packet_type, reason_code) do
    "Reason Code: #{reason_code}"
  end

  # Format QoS for display
  defp format_qos(qos, packet_type) when packet_type in ["PUBLISH", "PUBREL"] and is_integer(qos),
    do: qos

  defp format_qos(_, _), do: "-"

  # Calculate actual payload size for ACK messages
  # For ACK messages, we want to show the actual payload size (usually 0),
  # not the size of the formatted reason code text
  defp calculate_actual_payload_size(payload, packet_type) do
    ack_types = [
      "PUBACK",
      "PUBREC",
      "PUBREL",
      "PUBCOMP",
      "SUBACK",
      "UNSUBACK",
      "CONNACK",
      "DISCONNECT"
    ]

    if packet_type in ack_types do
      # For ACK messages, return the actual payload size (usually 0)
      case payload do
        payload when is_binary(payload) -> byte_size(payload)
        nil -> 0
        _ -> 0
      end
    else
      # For non-ACK messages, calculate normal payload size
      case payload do
        payload when is_binary(payload) -> byte_size(payload)
        nil -> 0
        _ -> 0
      end
    end
  end

  # Format subscription error message based on reason codes
  defp format_subscription_error(reason_codes) when is_list(reason_codes) do
    # Get the first error reason code (most relevant)
    first_error = Enum.find(reason_codes, &(&1 != 0))
    format_single_reason_code(first_error || 0)
  end

  # Format unsubscription error message based on reason codes
  defp format_unsubscription_error(reason_codes) when is_list(reason_codes) do
    # Get the first error reason code (most relevant)
    first_error = Enum.find(reason_codes, &(&1 != 0))
    format_single_reason_code(first_error || 0)
  end

  # Format a single reason code to human-readable message
  defp format_single_reason_code(reason_code) do
    case reason_code do
      0 -> "Success"
      128 -> "Unspecified error"
      129 -> "Implementation specific error"
      131 -> "Implementation specific error"
      135 -> "Not authorized"
      143 -> "Topic filter invalid"
      145 -> "Packet identifier in use"
      151 -> "Quota exceeded"
      158 -> "Shared subscriptions not supported"
      161 -> "Subscription identifiers not supported"
      162 -> "Wildcard subscriptions not supported"
      _ -> "Error (#{reason_code})"
    end
  end
end
